import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import apiService from "../../services/apiService";
import EnhancedLoadingScreen from "../UI/EnhancedLoadingScreen";
import useScrollToTop from "../../hooks/useScrollToTop";
import AssessmentRelation from "./AssessmentExplanations";

const ResultPersona = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState("");
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  // Scroll to top when component mounts or route changes
  useScrollToTop();

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage =
            retryCount >= maxRetries
              ? `Result not found after ${
                  maxRetries + 1
                } attempts. The analysis may still be processing.`
              : err.response?.data?.message || "Failed to load results";
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate("/dashboard");
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getProspectColor = (level) => {
    const colors = {
      "super high": "text-slate-900 bg-slate-100 border-slate-300",
      high: "text-slate-800 bg-slate-100 border-slate-300",
      moderate: "text-slate-700 bg-slate-100 border-slate-300",
      low: "text-slate-700 bg-slate-100 border-slate-300",
      "super low": "text-slate-700 bg-slate-100 border-slate-300",
    };
    return colors[level] || "text-slate-700 bg-slate-100 border-slate-300";
  };

  const getProspectLevel = (level) => {
    const levels = {
      "super high": 5,
      high: 4,
      moderate: 3,
      low: 2,
      "super low": 1,
    };
    return levels[level] || 0;
  };

  const formatProspectLabel = (key) => {
    const labels = {
      jobAvailability: "Ketersediaan Pekerjaan",
      salaryPotential: "Potensi Penghasilan",
      careerProgression: "Peluang Pengembangan Karier",
      industryGrowth: "Pertumbuhan Industri",
      skillDevelopment: "Pengembangan Keahlian",
    };
    return labels[key] || key;
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Minat Karier',
      subtitle: 'RIASEC Assessment',
      description: 'Jelajahi minat karier dan preferensi lingkungan kerja Anda.',
      path: `/results/${resultId}/riasec`,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: 'Trait Kepribadian',
      subtitle: 'OCEAN Assessment',
      description: 'Pahami dimensi kepribadian utama Anda.',
      path: `/results/${resultId}/ocean`,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Kekuatan Karakter',
      subtitle: 'VIA-IS Assessment',
      description: 'Temukan kekuatan inti dan nilai-nilai karakter Anda.',
      path: `/results/${resultId}/via-is`,
      color: 'from-purple-500 to-purple-600'
    }
  ];

  const renderPersonaProfile = (personaProfile) => {
    if (!personaProfile) {
      return <p className="text-gray-600">No persona profile available.</p>;
    }

    return (
      <div id="persona-profile-container" className="space-y-4">
        {/* Tipe Karier & Ringkasan */}
        <motion.div
          id="career-archetype-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden"
        >
          {/* Header */}
          <div id="archetype-header" className="bg-gray-50 border-b border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">✦</span>
              </div>
              <div>
                <h3 className="text-xl font-medium text-slate-900">
                  {personaProfile.archetype}
                </h3>
                <p className="text-slate-500 text-xs">Career Archetype Profile</p>
              </div>
            </div>
          </div>
          {/* Content */}
          <div id="archetype-content" className="p-4 space-y-4">
            <p className="text-slate-700 text-sm leading-relaxed">
              {personaProfile.shortSummary}
            </p>

            {/* Core Motivators */}
            {personaProfile.coreMotivators && personaProfile.coreMotivators.length > 0 && (
              <div id="core-motivators-section">
                <h4 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Motivator Utama</h4>
                <div className="flex flex-wrap gap-2">
                  {personaProfile.coreMotivators.map((motivator, idx) => (
                    <span
                      key={idx}
                      className="text-slate-700 bg-slate-50 border border-slate-200 px-3 py-1.5 rounded-md text-xs font-medium"
                    >
                      {motivator}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Learning Style */}
            {personaProfile.learningStyle && (
              <div id="learning-style-section">
                <h4 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Gaya Belajar</h4>
                <div className="bg-slate-50 p-3 rounded-lg border border-slate-200">
                  <p className="text-slate-700 text-xs leading-relaxed">
                    {personaProfile.learningStyle}
                  </p>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Core Strengths & Development Insights */}
        <div id="strengths-development-grid" className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Kekuatan Utama */}
          <motion.div
            id="core-strengths-section"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden"
          >
            {/* Header */}
            <div id="strengths-header" className="bg-emerald-50 border-b border-emerald-200 p-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-emerald-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <h4 className="text-sm font-medium text-slate-900">
                  Kekuatan Utama
                </h4>
              </div>
            </div>
            {/* Content */}
            <div id="strengths-content" className="p-3">
              {personaProfile.strengthSummary && (
                <div className="mb-3 bg-slate-50 p-3 rounded-lg border border-slate-200">
                  <p className="text-slate-700 text-xs leading-relaxed">
                    {personaProfile.strengthSummary}
                  </p>
                </div>
              )}
              <div className="space-y-2">
                {personaProfile.strengths?.map((strength, idx) => (
                  <div
                    key={idx}
                    className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                  >
                    <span className="text-emerald-500 mr-2 mt-0.5">•</span>
                    <span className="leading-relaxed">{strength}</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Pengembangan Keahlian */}
          {personaProfile.skillSuggestion &&
            personaProfile.skillSuggestion.length > 0 && (
              <div id="skill-development-section" className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                {/* Header */}
                <div id="skill-development-header" className="bg-blue-50 border-b border-blue-200 p-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                      <span className="text-white text-xs">⚡</span>
                    </div>
                    <h4 className="text-sm font-medium text-slate-900">
                      Pengembangan Keahlian
                    </h4>
                  </div>
                </div>
                {/* Content */}
                <div id="skill-development-content" className="p-3">
                  <div className="mb-3 bg-slate-50 p-3 rounded-lg border border-slate-200">
                    <p className="text-slate-700 text-xs leading-relaxed">
                      Rekomendasi keahlian yang dirancang khusus untuk memperkokoh fondasi keunggulan utama yang telah Anda miliki.
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {personaProfile.skillSuggestion.map((skill, idx) => (
                      <span
                        key={idx}
                        className="text-slate-700 bg-slate-50 border border-slate-200 px-2 py-1 rounded text-xs font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
        </div>

        {/* Rekomendasi Karier */}
        <motion.div
          id="career-recommendations-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden"
        >
          {/* Header */}
          <div id="career-recommendations-header" className="bg-purple-50 border-b border-purple-200 p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">💼</span>
              </div>
              <div>
                <h4 className="text-lg font-medium text-slate-900">
                  Rekomendasi Karier
                </h4>
                <p className="text-purple-600 text-xs">Jalur Karier yang Sesuai dengan Profil Anda</p>
              </div>
            </div>
          </div>
          {/* Content */}
          <div id="career-recommendations-content" className="p-4">
            <div className="space-y-4">
              {personaProfile.careerRecommendation?.map((career, idx) => (
                <div
                  key={idx}
                  id={`career-option-${idx}`}
                  className="bg-slate-50 rounded-lg border border-slate-200 overflow-hidden"
                >
                  {/* Career Header */}
                  <div id={`career-header-${idx}`} className="bg-white border-b border-slate-200 p-3">
                    <h5 className="text-lg font-medium text-slate-900 text-center">
                      {career.careerName}
                    </h5>
                  </div>

                  <div className="p-3 space-y-3">
                    {/* Justification */}
                    {career.justification && (
                      <div id={`career-justification-${idx}`}>
                        <h6 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Mengapa Cocok untuk Anda</h6>
                        <div className="bg-white p-3 rounded-lg border border-slate-200">
                          <p className="text-xs text-slate-700 leading-relaxed">
                            {career.justification}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Career Prospects */}
                    <div id={`career-prospects-${idx}`}>
                      <h6 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Prospek Karier</h6>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2">
                        {Object.entries(career.careerProspect || {}).map(
                          ([key, value]) => (
                            <div key={key} className="text-center bg-white p-2 rounded border border-slate-200">
                              <div className="text-lg font-medium text-slate-900 mb-1">
                                {getProspectLevel(value)}/5
                              </div>
                              <div className={`text-xs font-medium mb-2 px-2 py-1 rounded ${getProspectColor(value)}`}>
                                {value}
                              </div>
                              <div className="text-xs text-slate-600 leading-tight">
                                {formatProspectLabel(key)}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    {/* First Steps */}
                    {career.firstSteps && career.firstSteps.length > 0 && (
                      <div id={`career-first-steps-${idx}`}>
                        <h6 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Langkah Pertama</h6>
                        <div className="space-y-2">
                          {career.firstSteps.map((step, stepIdx) => (
                            <div
                              key={stepIdx}
                              className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                            >
                              <div className="w-4 h-4 bg-blue-600 rounded flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                                <span className="text-white text-xs">{stepIdx + 1}</span>
                              </div>
                              <span className="leading-relaxed">{step}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Related Majors */}
                    {career.relatedMajors && career.relatedMajors.length > 0 && (
                      <div id={`career-related-majors-${idx}`}>
                        <h6 className="text-xs font-semibold text-slate-900 mb-2 uppercase tracking-wider">Jurusan Terkait</h6>
                        <div className="flex flex-wrap gap-2">
                          {career.relatedMajors.map((major, majorIdx) => (
                            <span
                              key={majorIdx}
                              className="text-slate-700 bg-white border border-slate-200 px-2 py-1 rounded text-xs font-medium"
                            >
                              {major}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Development Areas & Skills Grid */}
        <div id="development-areas-grid" className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Area Pengembangan */}
          <div id="development-areas-section" className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            {/* Header */}
            <div id="development-areas-header" className="bg-amber-50 border-b border-amber-200 p-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-amber-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs">⚡</span>
                </div>
                <h4 className="text-sm font-medium text-slate-900">
                  Area Pengembangan
                </h4>
              </div>
            </div>
            {/* Content */}
            <div id="development-areas-content" className="p-3">
              {personaProfile.weaknessSummary && (
                <div className="mb-3 bg-slate-50 p-3 rounded-lg border border-slate-200">
                  <p className="text-slate-700 text-xs leading-relaxed">
                    {personaProfile.weaknessSummary}
                  </p>
                </div>
              )}
              <div className="space-y-2">
                {personaProfile.weaknesses?.map((weakness, idx) => (
                  <div
                    key={idx}
                    className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                  >
                    <span className="text-amber-500 mr-2 mt-0.5">•</span>
                    <span className="leading-relaxed">{weakness}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Wawasan Pengembangan */}
          <motion.div
            id="development-insights-section"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden"
          >
            {/* Header */}
            <div id="development-insights-header" className="bg-teal-50 border-b border-teal-200 p-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-teal-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs">💡</span>
                </div>
                <h4 className="text-sm font-medium text-slate-900">
                  Wawasan Pengembangan
                </h4>
              </div>
            </div>
            {/* Content */}
            <div id="development-insights-content" className="p-3">
              <div className="mb-3 bg-slate-50 p-3 rounded-lg border border-slate-200">
                <p className="text-slate-700 text-xs leading-relaxed">
                  Wawasan strategis yang mendalam untuk membantu Anda mengenali dan menyikapi area-area yang perlu dikembangkan.
                </p>
              </div>
              <div className="space-y-2">
                {personaProfile.insights?.map((insight, idx) => (
                  <div
                    key={idx}
                    className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                  >
                    <span className="text-teal-500 mr-2 mt-0.5">•</span>
                    <span className="leading-relaxed">{insight}</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Tantangan Potensial - Full Width */}
        {personaProfile.possiblePitfalls &&
          personaProfile.possiblePitfalls.length > 0 && (
            <div id="potential-challenges-section" className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              {/* Header */}
              <div id="potential-challenges-header" className="bg-red-50 border-b border-red-200 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">⚠</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-slate-900">
                      Tantangan Potensial
                    </h4>
                    <p className="text-red-600 text-xs">Area yang Perlu Diwaspadai</p>
                  </div>
                </div>
              </div>
              {/* Content */}
              <div id="potential-challenges-content" className="p-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {personaProfile.possiblePitfalls.map((pitfall, idx) => (
                    <div
                      key={idx}
                      className="flex items-start text-xs text-slate-700 bg-slate-50 p-3 rounded-lg border border-slate-200"
                    >
                      <span className="text-red-500 mr-2 mt-0.5">•</span>
                      <span className="leading-relaxed">{pitfall}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

        {/* Work Environment & Role Models */}
        <div id="work-environment-grid" className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Lingkungan Kerja */}
          {personaProfile.workEnvironment && (
            <div id="work-environment-section" className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              {/* Header */}
              <div id="work-environment-header" className="bg-indigo-50 border-b border-indigo-200 p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-indigo-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs">🏢</span>
                  </div>
                  <h4 className="text-sm font-medium text-slate-900">
                    Lingkungan Kerja
                  </h4>
                </div>
              </div>
              {/* Content */}
              <div id="work-environment-content" className="p-3">
                <div className="bg-slate-50 p-3 rounded-lg border border-slate-200">
                  <p className="text-slate-700 leading-relaxed text-xs">
                    {personaProfile.workEnvironment}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Panutan Karier */}
          {personaProfile.roleModel && personaProfile.roleModel.length > 0 && (
            <div id="role-models-section" className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              {/* Header */}
              <div id="role-models-header" className="bg-violet-50 border-b border-violet-200 p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-violet-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs">👥</span>
                  </div>
                  <h4 className="text-sm font-medium text-slate-900">
                    Panutan Karier
                  </h4>
                </div>
              </div>
              {/* Content */}
              <div id="role-models-content" className="p-3">
                <div className="flex flex-wrap gap-2">
                  {personaProfile.roleModel.map((model, idx) => (
                    <span
                      key={idx}
                      className="bg-slate-50 text-slate-700 border border-slate-200 px-2 py-1 rounded text-xs font-medium"
                    >
                      {model}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Development Activities Section */}
        {personaProfile.developmentActivities && (
          <motion.div
            id="development-activities-section"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden"
          >
            {/* Header */}
            <div id="development-activities-header" className="bg-emerald-50 border-b border-emerald-200 p-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">🚀</span>
                </div>
                <div>
                  <h4 className="text-lg font-medium text-slate-900">
                    Aktivitas Pengembangan
                  </h4>
                  <p className="text-emerald-600 text-xs">Rekomendasi Kegiatan untuk Pertumbuhan</p>
                </div>
              </div>
            </div>
            {/* Content */}
            <div id="development-activities-content" className="p-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Extracurricular Activities */}
                {personaProfile.developmentActivities.extracurricular &&
                 personaProfile.developmentActivities.extracurricular.length > 0 && (
                  <div id="extracurricular-activities" className="bg-slate-50 rounded-lg border border-slate-200 p-3">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs">🎯</span>
                      </div>
                      <h5 className="text-xs font-semibold text-slate-900 uppercase tracking-wider">
                        Ekstrakurikuler
                      </h5>
                    </div>
                    <div className="space-y-2">
                      {personaProfile.developmentActivities.extracurricular.map((activity, idx) => (
                        <div
                          key={idx}
                          className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                        >
                          <span className="text-blue-500 mr-2 mt-0.5">•</span>
                          <span className="leading-relaxed">{activity}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Project Ideas */}
                {personaProfile.developmentActivities.projectIdeas &&
                 personaProfile.developmentActivities.projectIdeas.length > 0 && (
                  <div id="project-ideas" className="bg-slate-50 rounded-lg border border-slate-200 p-3">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-purple-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs">💡</span>
                      </div>
                      <h5 className="text-xs font-semibold text-slate-900 uppercase tracking-wider">
                        Ide Proyek
                      </h5>
                    </div>
                    <div className="space-y-2">
                      {personaProfile.developmentActivities.projectIdeas.map((project, idx) => (
                        <div
                          key={idx}
                          className="flex items-start text-xs text-slate-700 bg-white p-2 rounded border border-slate-200"
                        >
                          <span className="text-purple-500 mr-2 mt-0.5">•</span>
                          <span className="leading-relaxed">{project}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Book Recommendations */}
                {personaProfile.developmentActivities.bookRecommendations &&
                 personaProfile.developmentActivities.bookRecommendations.length > 0 && (
                  <div id="book-recommendations" className="bg-slate-50 rounded-lg border border-slate-200 p-3">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-amber-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs">📚</span>
                      </div>
                      <h5 className="text-xs font-semibold text-slate-900 uppercase tracking-wider">
                        Rekomendasi Buku
                      </h5>
                    </div>
                    <div className="space-y-3">
                      {personaProfile.developmentActivities.bookRecommendations.map((book, idx) => (
                        <div
                          key={idx}
                          className="bg-white p-3 rounded border border-slate-200"
                        >
                          <h6 className="text-xs font-semibold text-slate-900 mb-1 leading-tight">
                            {book.title}
                          </h6>
                          <p className="text-xs text-slate-500 mb-2">
                            oleh {book.author}
                          </p>
                          <p className="text-xs text-slate-700 leading-relaxed">
                            {book.reason}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </div>
    );
  };

  return (
    <div id="result-persona-page" className="min-h-screen bg-gray-50">
      {/* Main Content Area */}
      <main id="main-content" className="flex-1 max-w-6xl mx-auto px-4 py-6">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            id="loading-state"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading Career Persona..."
              subtitle="Fetching your detailed career profile"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            id="error-state"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <svg
                    className="h-4 w-4 text-red-600"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-slate-900">
                  Unable to Load Results
                </h3>
                <div className="mt-2 text-sm text-slate-600">
                  <p>{error}</p>
                </div>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-slate-800 text-white px-4 py-2 rounded-lg text-sm hover:bg-slate-700 transition-all duration-200"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-slate-50 text-slate-700 px-4 py-2 rounded-lg text-sm hover:bg-slate-100 transition-all duration-200 border border-slate-200"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              id="page-header"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 space-y-4 sm:space-y-0">
                <div>
                  <h1 className="text-2xl font-medium text-slate-900 mb-2">
                    Career Persona Profile
                  </h1>
                  <p className="text-slate-600 max-w-2xl text-sm leading-relaxed">
                    Discover your comprehensive career persona based on integrated assessment results from RIASEC, OCEAN, and VIA-IS evaluations.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-4 py-2 border border-slate-200 text-slate-700 rounded-lg hover:bg-slate-50 transition-all duration-200 text-sm"
                  >
                    ← Back
                  </button>
                  <button
                    onClick={() => navigate("/dashboard")}
                    className="px-4 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-700 transition-all duration-200 text-sm"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div id="header-info-card" className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs text-slate-600 space-y-3 sm:space-y-0">
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-slate-800 rounded-full mr-2"></div>
                      <span>Completed: {formatDate(result.created_at)}</span>
                    </div>
                    {(() => {
                      const riskTolerance = result.persona_profile?.riskTolerance || result.persona_profile?.risk_tolerance;
                      return riskTolerance && (
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                          <span>Risk Profile: <span className="font-medium capitalize text-slate-800">{riskTolerance}</span></span>
                        </div>
                      );
                    })()}
                  </div>
                  <span className="bg-slate-100 text-slate-700 px-3 py-1 rounded text-xs font-medium uppercase tracking-wider">
                    Integrated Profile
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Persona Profile Content */}
            <motion.div
              id="persona-profile-main"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className="text-center mb-6">
                <h2 className="text-xl font-medium text-slate-900 mb-2">
                  Your Complete Career Profile
                </h2>
                <div className="w-16 h-0.5 bg-slate-300 mx-auto rounded-full"></div>
              </div>
              {renderPersonaProfile(result.persona_profile)}
            </motion.div>

            

            {/* Navigation to Other Results */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.2 }}
              className="mb-6"
            >
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-slate-100 rounded-lg mb-2">
                    <span className="text-lg">🎯</span>
                  </div>
                  <h2 className="text-xl font-medium text-slate-900 mb-2">
                    Jelajahi Profil Lengkap Anda
                  </h2>
                  <p className="text-slate-600 text-sm max-w-2xl mx-auto leading-relaxed">
                    Lanjutkan perjalanan Anda dengan mengeksplorasi aspek lain dari hasil assessment. Setiap assessment memberikan wawasan unik tentang berbagai sisi kepribadian dan potensi karier Anda.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-w-5xl mx-auto">
                {navigationCards.map((card, index) => (
                  <motion.div
                    key={card.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.3 + index * 0.1 }}
                    whileHover={{
                      y: -2,
                      transition: { duration: 0.15 }
                    }}
                    className="group cursor-pointer"
                    onClick={() => navigate(card.path)}
                  >
                    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 h-full">
                      <div className="flex flex-col h-full">
                        <div className="flex items-start justify-end mb-2">
                          <motion.svg
                            className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            whileHover={{ x: 2 }}
                            transition={{ duration: 0.2 }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </motion.svg>
                        </div>

                        <div className="flex-grow">
                          <h3 className="text-lg font-medium text-slate-900 mb-1 group-hover:text-slate-700 transition-colors">
                            {card.title}
                          </h3>
                          <p className="text-xs text-slate-500 mb-2 font-medium uppercase tracking-wide">
                            {card.subtitle}
                          </p>
                          <p className="text-slate-600 leading-relaxed text-sm">
                            {card.description}
                          </p>
                        </div>

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex items-center text-xs font-medium text-slate-500 group-hover:text-slate-800 transition-colors">
                            <span>Lihat Assessment</span>
                            <motion.svg
                              className="w-3 h-3 ml-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              whileHover={{ x: 1 }}
                              transition={{ duration: 0.2 }}
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </motion.svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </main>
    </div>
  );
};

export default ResultPersona;
